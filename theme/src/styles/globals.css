@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --bg-primary: #f2f2f2;
  --bg-secondary: #f9fafb;
  --text-primary: #111827;
  --text-secondary: #4b5563;
  --border-color: #e5e7eb;
}

.dark {
  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --text-primary: #ffffff;
  --text-secondary: #f2f2f2;
  --border-color: #2a2a2a;
}

/* 科技感网格背景 */
.tech-grid-background {
  background: 
    linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px),
    linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
    linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  background-size: 50px 50px, 50px 50px, 100% 100%;
  position: relative;
}

.dark .tech-grid-background {
  background: 
    linear-gradient(90deg, rgba(96, 165, 250, 0.15) 1px, transparent 1px),
    linear-gradient(rgba(96, 165, 250, 0.15) 1px, transparent 1px),
    linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  background-size: 50px 50px, 50px 50px, 100% 100%;
}

.tech-grid-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 25% 25%, rgba(168, 85, 247, 0.2) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.2) 1px, transparent 1px);
  background-size: 100px 100px;
  animation: techPulse 8s ease-in-out infinite;
  pointer-events: none;
}

.dark .tech-grid-background::before {
  background: 
    radial-gradient(circle at 25% 25%, rgba(168, 85, 247, 0.3) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(96, 165, 250, 0.3) 1px, transparent 1px);
  background-size: 100px 100px;
}

@keyframes techPulse {
  0%, 100% { 
    opacity: 0.5; 
    transform: scale(1); 
  }
  50% { 
    opacity: 1; 
    transform: scale(1.05); 
  }
}

/* 适用于浅色主题的网格背景 */
.tech-grid-background-light {
  background: 
    linear-gradient(90deg, rgba(59, 130, 246, 0.08) 1px, transparent 1px),
    linear-gradient(rgba(59, 130, 246, 0.08) 1px, transparent 1px),
    linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  background-size: 50px 50px, 50px 50px, 100% 100%;
  position: relative;
}

.tech-grid-background-light::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 25% 25%, rgba(168, 85, 247, 0.1) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  background-size: 100px 100px;
  animation: techPulse 8s ease-in-out infinite;
  pointer-events: none;
}

@layer base {
  body {
    @apply bg-theme-bg-primary text-theme-text-primary transition-colors;
  }

  body.dark {
    @apply bg-[#1a1a1a];
  }
}

@layer components {
  .nav-link {
    @apply px-4 py-2 text-theme-text-secondary hover:text-primary-500 font-medium text-sm transition-colors relative
    after:absolute after:left-0 after:bottom-0 after:h-0.5 after:w-0 after:bg-primary-500
    after:transition-all hover:after:w-full after:shadow-[0_0_10px_rgba(129,200,105,0.3)];
  }
  
  .btn-primary {
    @apply px-6 py-2 bg-primary-500 text-white rounded-full font-medium text-sm
    shadow-lg shadow-primary-500/30 hover:shadow-primary-500/50 hover:bg-primary-400
    transform hover:-translate-y-0.5 transition-all duration-200
    border border-primary-400/20 backdrop-blur-sm;
  }

  .prose {
    @apply max-w-none;
  }
  
  .prose h1 {
    @apply text-4xl font-bold mb-8
    bg-gradient-to-r from-primary-500 to-primary-400 bg-clip-text text-transparent
    drop-shadow-sm;
  }
  
  .prose h2 {
    @apply text-2xl font-semibold text-theme-text-primary mt-12 mb-6 pb-2
    border-b border-theme-border;
  }
  
  .prose h3 {
    @apply text-xl font-medium text-primary-500 mt-8 mb-4;
  }
  
  .prose p {
    @apply text-theme-text-secondary leading-relaxed mb-6;
  }
  
  .prose a {
    @apply text-primary-500 hover:text-primary-400 font-medium transition-colors
    hover:underline decoration-2 decoration-primary-300;
  }
  
  .prose ul {
    @apply space-y-2 my-6 list-none pl-6;
  }
  
  .prose ul li {
    @apply relative text-theme-text-secondary;
  }

  .prose ul li::before {
    @apply content-[''] absolute w-2 h-2 -left-4 top-2.5
    bg-primary-500 rounded-full;
  }
  
  .prose ol {
    @apply space-y-2 my-6 list-decimal pl-6;
  }
  
  .prose li {
    @apply text-theme-text-secondary;
  }

  .prose code {
    @apply text-primary-500 bg-primary-500/5 px-1.5 py-0.5 rounded text-sm;
  }

  .prose pre {
    @apply bg-theme-bg-secondary text-theme-text-primary p-4 rounded-lg my-6 overflow-auto
    border border-theme-border shadow-sm;
  }

  .prose pre code {
    @apply bg-transparent text-inherit p-0 border-0;
  }

  .prose blockquote {
    @apply border-l-4 border-primary-500 bg-gradient-to-r from-primary-500/5 to-transparent 
    pl-4 py-2 my-6 italic;
  }

  .prose table {
    @apply w-full my-6 overflow-hidden rounded-lg border border-theme-border;
  }

  .prose th {
    @apply bg-theme-bg-secondary text-theme-text-primary font-semibold text-left p-3
    border-b border-theme-border;
  }

  .prose td {
    @apply p-3 text-theme-text-secondary border-b border-theme-border
    bg-theme-bg-primary hover:bg-theme-bg-secondary transition-colors;
  }

  /* 隐藏滚动条但保持滚动功能 */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
} 