.container {
    min-height: 100vh;
    background-color: var(--bg-primary);
}

.content {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* 组件样式 */
.navLink {
    padding: 0.5rem 1rem;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.875rem;
    transition: color 0.2s ease;
    position: relative;
}

.navLink:hover {
    color: var(--primary-500);
}

.navLink::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    height: 2px;
    width: 0;
    background-color: var(--primary-500);
    transition: all 0.2s ease;
    box-shadow: 0 0 10px rgba(129,200,105,0.3);
}

.navLink:hover::after {
    width: 100%;
}

.btnPrimary {
    padding: 0.5rem 1.5rem;
    background-color: var(--primary-500);
    color: white;
    border-radius: 9999px;
    font-weight: 500;
    font-size: 0.875rem;
    box-shadow: 0 10px 15px -3px rgba(16, 185, 129, 0.3);
    border: 1px solid rgba(52, 211, 153, 0.2);
    backdrop-filter: blur(4px);
    transform: translateY(0);
    transition: all 0.2s ease;
}

.btnPrimary:hover {
    background-color: var(--primary-400);
    box-shadow: 0 10px 15px -3px rgba(16, 185, 129, 0.5);
    transform: translateY(-2px);
}

/* Prose Styles */
.prose {
    max-width: none;
}

.prose h1 {
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: 2rem;
    background: linear-gradient(to right, var(--primary-500), var(--primary-400));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    filter: drop-shadow(0 1px 1px rgba(0,0,0,0.05));
}

.prose h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-top: 3rem;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.prose h3 {
    font-size: 1.25rem;
    font-weight: 500;
    color: var(--primary-500);
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.prose p {
    color: var(--text-secondary);
    line-height: 1.75;
    margin-bottom: 1.5rem;
}

.prose a {
    color: var(--primary-500);
    font-weight: 500;
    text-decoration: none;
    transition: color 0.2s ease;
}

.prose a:hover {
    color: var(--primary-400);
    text-decoration: underline;
    text-decoration-thickness: 2px;
    text-decoration-color: var(--primary-300);
}

.prose ul {
    margin: 1.5rem 0;
    padding-left: 1.5rem;
    list-style: none;
}

.prose ul li {
    position: relative;
    color: var(--text-secondary);
    margin: 0.5rem 0;
}

.prose ul li::before {
    content: '';
    position: absolute;
    width: 0.5rem;
    height: 0.5rem;
    left: -1rem;
    top: 0.625rem;
    background-color: var(--primary-500);
    border-radius: 50%;
}

.prose ol {
    margin: 1.5rem 0;
    padding-left: 1.5rem;
    list-style-type: decimal;
}

.prose li {
    color: var(--text-secondary);
    margin: 0.5rem 0;
}

.prose code {
    color: var(--primary-500);
    background-color: rgba(16, 185, 129, 0.05);
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.prose pre {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 1.5rem 0;
    overflow: auto;
    border: 1px solid var(--border-color);
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.prose pre code {
    background-color: transparent;
    color: inherit;
    padding: 0;
    border: none;
}

.prose blockquote {
    border-left: 4px solid var(--primary-500);
    background: linear-gradient(to right, rgba(16, 185, 129, 0.05), transparent);
    padding: 0.5rem 0 0.5rem 1rem;
    margin: 1.5rem 0;
    font-style: italic;
}

.prose table {
    width: 100%;
    margin: 1.5rem 0;
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
    border-collapse: collapse;
    overflow: hidden;
}

.prose th {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-weight: 600;
    text-align: left;
    padding: 0.75rem;
    border-bottom: 1px solid var(--border-color);
}

.prose td {
    padding: 0.75rem;
    color: var(--text-secondary);
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-primary);
    transition: background-color 0.2s ease;
}

.prose td:hover {
    background-color: var(--bg-secondary);
}