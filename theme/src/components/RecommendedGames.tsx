import React from 'react';
import Link from 'next/link';

interface RecommendedGame {
    title: string;
    image: string;
    url: string;
    description?: string;
}

interface RecommendedGamesProps {
    title?: string;
    description?: string;
    games: RecommendedGame[];
}

export function RecommendedGames({ 
    title = "推荐游戏", 
    description = "更多精彩游戏等你来玩",
    games 
}: RecommendedGamesProps) {
    return (
        <section className="relative overflow-hidden mb-8">
            <div className="max-w-5xl mx-auto px-2 sm:px-4 md:px-6 lg:px-8 relative">

                {/* 游戏列表 */}
                <div className="relative">
                    {/* 渐变遮罩 - 左侧 */}
                    <div className="absolute left-0 top-0 z-10 w-6 h-full bg-gradient-to-r from-black/60 to-transparent pointer-events-none md:hidden"></div>
                    
                    {/* 渐变遮罩 - 右侧 */}
                    <div className="absolute right-0 top-0 z-10 w-6 h-full bg-gradient-to-l from-black/60 to-transparent pointer-events-none md:hidden"></div>
                    
                    {/* 游戏网格 */}
                    <div className="flex gap-2 sm:gap-3 md:gap-4 overflow-x-auto scrollbar-hide pb-4 md:grid md:grid-cols-4 lg:grid-cols-6 md:overflow-visible md:pb-0 px-2 md:px-0">
                        {games.map((game, index) => (
                            <div key={index} className="group relative flex-shrink-0 w-20 sm:w-24 md:w-auto">
                                <div className="absolute -inset-0.5 bg-gradient-to-r from-primary-500 to-indigo-500 rounded-lg blur opacity-20 group-hover:opacity-40 transition-all"></div>
                                <Link href={game.url} className="relative block">
                                    <div className="relative bg-black/40 backdrop-blur-sm rounded-lg overflow-hidden transform hover:scale-105 transition-all shadow-lg border border-white/10 aspect-square">
                                        {/* 游戏封面 */}
                                        <div className="relative w-full h-full overflow-hidden">
                                            <img 
                                                src={game.image} 
                                                alt={game.title}
                                                className="w-full h-full object-cover transition-transform group-hover:scale-110"
                                            />
                                            
                                            {/* 悬浮播放按钮 */}
                                            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center">
                                                <div className="w-8 h-8 rounded-full bg-primary-500 text-white flex items-center justify-center opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0 transition-all">
                                                    <svg className="w-4 h-4 ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                                                        <path d="M8 5v14l11-7z"/>
                                                    </svg>
                                                </div>
                                            </div>

                                            {/* 游戏标题叠加层 */}
                                            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent p-1 sm:p-1.5 md:p-2">
                                                <p className="text-xs font-semibold text-white line-clamp-2 text-center leading-tight drop-shadow-lg">
                                                    {game.title}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </Link>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </section>
    );
} 