import { useRouter } from 'nextra/hooks'
import Link from 'next/link'
import { Fragment } from 'react'
import { Icon } from '@iconify/react'
import { SITE_CONFIG } from '../../../config/site.js'

interface BreadcrumbItem {
    name: string
    href: string
}

export function Breadcrumb() {
    const router = useRouter()
    const { asPath, locale } = router

    // 检查是否启用了多语言功能
    const isI18nEnabled = SITE_CONFIG.features.i18n

    // 生成面包屑路径
    const generateBreadcrumb = (): BreadcrumbItem[] => {
        // 移除查询参数
        const pathWithoutQuery = asPath.split('?')[0]
        const paths = pathWithoutQuery.split('/').filter(Boolean)
        const items: BreadcrumbItem[] = []

        // 添加首页 - 根据多语言功能决定链接格式
        items.push({
            name: (isI18nEnabled && locale === 'zh') ? '首页' : 'Home',
            href: isI18nEnabled ? `/${locale}` : '/'
        })

        // 构建路径
        let currentPath = ''
        paths.forEach((path, index) => {
            // 如果启用了多语言且第一个路径是语言代码，则跳过
            if (isI18nEnabled && index === 0 && Object.keys(SITE_CONFIG.i18nConfig.locales || {}).includes(path)) {
                return
            }
            currentPath += `/${path}`
            
            // 根据路径生成名称
            let name = path
            switch (path) {
                case 'games':
                    name = (isI18nEnabled && locale === 'zh') ? '游戏' : 'Games'
                    break
                case 'about':
                    name = (isI18nEnabled && locale === 'zh') ? '关于' : 'About'
                    break
                case 'download':
                    name = (isI18nEnabled && locale === 'zh') ? '下载' : 'Download'
                    break
            }

            items.push({
                name,
                href: isI18nEnabled ? `/${locale}${currentPath}` : currentPath
            })
        })

        return items
    }

    const breadcrumbs = generateBreadcrumb()

    return (
        <nav className="flex items-center gap-2 text-sm text-theme-text-secondary mb-6">
            {/* 首页图标 */}
            <Link 
                href={breadcrumbs[0].href}
                className="hover:text-primary-500 transition-colors"
            >
                <Icon icon="material-symbols:home" className="w-4 h-4" />
            </Link>

            {/* 其他导航项 */}
            {breadcrumbs.slice(1).map((item, index) => (
                <Fragment key={item.href}>
                    <Icon 
                        icon="material-symbols:chevron-right" 
                        className="w-4 h-4 opacity-50 text-theme-text-secondary"
                    />
                    <Link
                        href={item.href}
                        className={`
                            transition-colors hover:text-primary-500
                            ${index === breadcrumbs.length - 2 
                                ? 'text-primary-500 font-medium' 
                                : 'text-theme-text-secondary'
                            }
                        `}
                    >
                        {item.name}
                    </Link>
                </Fragment>
            ))}
        </nav>
    )
} 