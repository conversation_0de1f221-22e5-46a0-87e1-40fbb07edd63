import React from 'react'
import { Icon } from '@iconify/react'
import {
    FacebookShareButton,
    TwitterShareButton,
    TelegramShareButton,
    WhatsappShareButton,
    FacebookIcon,
    TwitterIcon,
    TelegramIcon,
    WhatsappIcon
} from 'react-share'
import { useTheme } from 'next-themes'

interface GameFrameProps {
    src: string;
    title: string;
    cover?: string;
}

export function GameFrame({ src, title, cover }: GameFrameProps) {
    const [key, setKey] = React.useState(0);
    const [isFullscreen, setIsFullscreen] = React.useState(false);
    const [showTip, setShowTip] = React.useState(false);
    const [isLoaded, setIsLoaded] = React.useState(false);
    const [showShareMenu, setShowShareMenu] = React.useState(false);
    const [isMobile, setIsMobile] = React.useState(false);
    const [fullscreenSupported, setFullscreenSupported] = React.useState(false);
    const [screenInfo, setScreenInfo] = React.useState({
        width: 0,
        height: 0,
        isPortrait: false,
        isSmallScreen: false,
        deviceType: 'desktop' as 'desktop' | 'tablet' | 'mobile'
    });
    const [showOrientationTip, setShowOrientationTip] = React.useState(false);
    const iframeRef = React.useRef<HTMLIFrameElement>(null);
    const shareMenuRef = React.useRef<HTMLDivElement>(null);
    const { theme } = useTheme();

    // 动态屏幕适配检测
    React.useEffect(() => {
        const updateScreenInfo = () => {
            const width = window.innerWidth;
            const height = window.innerHeight;
            const isPortrait = height > width;
            const userAgent = navigator.userAgent.toLowerCase();
            const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
            
            // 更精确的设备类型判断
            let deviceType: 'desktop' | 'tablet' | 'mobile' = 'desktop';
            if (isMobileDevice) {
                if (width >= 768) {
                    deviceType = 'tablet';
                } else {
                    deviceType = 'mobile';
                }
            }
            
            const isSmallScreen = width < 640; // Tailwind sm breakpoint
            
            setScreenInfo({
                width,
                height,
                isPortrait,
                isSmallScreen,
                deviceType
            });
            
            setIsMobile(isMobileDevice);
            
            // 检查全屏API支持（带浏览器前缀）
            const element = document.documentElement as any;
            const isSupported = !!(
                element.requestFullscreen ||
                element.webkitRequestFullscreen ||
                element.mozRequestFullScreen ||
                element.msRequestFullscreen
            );
            
            // iOS Safari 特殊处理 - 即使支持API，iframe全屏也有问题
            const isIOS = /ipad|iphone|ipod/.test(userAgent);
            setFullscreenSupported(isSupported && !isIOS);
            
            // 移动端竖屏时显示横屏提示
            if (isMobileDevice && isPortrait && isLoaded && width < 768) {
                setShowOrientationTip(true);
            } else {
                setShowOrientationTip(false);
            }
        };

        updateScreenInfo();
        window.addEventListener('resize', updateScreenInfo);
        window.addEventListener('orientationchange', () => {
            // 延迟更新，等待设备完成旋转
            setTimeout(updateScreenInfo, 500);
        });
        
        return () => {
            window.removeEventListener('resize', updateScreenInfo);
            window.removeEventListener('orientationchange', updateScreenInfo);
        };
    }, [isLoaded]);

    // 根据设备和屏幕信息获取游戏容器样式
    const getGameContainerStyle = () => {
        const { deviceType, isPortrait, width, height, isSmallScreen } = screenInfo;
        
        if (deviceType === 'mobile') {
            if (isPortrait) {
                // 竖屏手机：使用更高的宽高比，减少上下留白
                return 'aspect-[4/3]';
            } else {
                // 横屏手机：接近全屏比例
                return 'aspect-[16/10]';
            }
        } else if (deviceType === 'tablet') {
            if (isPortrait) {
                // 竖屏平板：方形比例
                return 'aspect-square';
            } else {
                // 横屏平板：标准游戏比例
                return 'aspect-[16/10]';
            }
        } else {
            // 桌面端：保持原来的视频比例
            return 'aspect-video';
        }
    };

    // 获取游戏容器的额外样式
    const getGameContainerClass = () => {
        const { deviceType, isSmallScreen } = screenInfo;
        let baseClass = 'relative w-full bg-dark-secondary';
        
        if (deviceType === 'mobile') {
            // 移动端增加最小高度，避免游戏区域太小
            baseClass += isSmallScreen ? ' min-h-[250px]' : ' min-h-[300px]';
        } else if (deviceType === 'tablet') {
            baseClass += ' min-h-[400px]';
        }
        
        return `${baseClass} ${getGameContainerStyle()}`;
    };

    const handleReload = () => {
        setKey(prev => prev + 1);
    };

    const handlePlay = () => {
        setIsLoaded(true);
    };

    // 兼容性全屏API
    const requestFullscreen = (element: HTMLElement) => {
        const elem = element as any;
        if (elem.requestFullscreen) {
            return elem.requestFullscreen();
        } else if (elem.webkitRequestFullscreen) {
            return elem.webkitRequestFullscreen();
        } else if (elem.mozRequestFullScreen) {
            return elem.mozRequestFullScreen();
        } else if (elem.msRequestFullscreen) {
            return elem.msRequestFullscreen();
        }
        return Promise.reject(new Error('Fullscreen not supported'));
    };

    const exitFullscreen = () => {
        const doc = document as any;
        if (doc.exitFullscreen) {
            return doc.exitFullscreen();
        } else if (doc.webkitExitFullscreen) {
            return doc.webkitExitFullscreen();
        } else if (doc.mozCancelFullScreen) {
            return doc.mozCancelFullScreen();
        } else if (doc.msExitFullscreen) {
            return doc.msExitFullscreen();
        }
        return Promise.reject(new Error('Exit fullscreen not supported'));
    };

    const getFullscreenElement = () => {
        const doc = document as any;
        return doc.fullscreenElement || 
               doc.webkitFullscreenElement || 
               doc.mozFullScreenElement || 
               doc.msFullscreenElement;
    };

    const handleFullscreen = async () => {
        if (!iframeRef.current) return;

        // 移动端不支持全屏时的优化处理
        if (!fullscreenSupported) {
            if (isMobile) {
                // 移动端滚动优化 + 建议横屏
                setShowTip(true);
                setTimeout(() => setShowTip(false), 3000);
                
                // 滚动到游戏区域
                iframeRef.current.scrollIntoView({ 
                    behavior: 'smooth', 
                    block: 'center' 
                });
                
                // 如果是竖屏且屏幕较小，建议横屏
                if (screenInfo.isPortrait && screenInfo.width < 768) {
                    setTimeout(() => {
                        setShowOrientationTip(true);
                    }, 1000);
                }
                
                // 简单的"假全屏"处理
                setIsFullscreen(true);
                setTimeout(() => setIsFullscreen(false), 100);
                return;
            } else {
                alert('Your browser does not support fullscreen functionality');
                return;
            }
        }

        try {
            const fullscreenElement = getFullscreenElement();
            if (!fullscreenElement) {
                // 尝试对整个游戏容器进行全屏，而不是iframe
                const gameContainer = iframeRef.current.parentElement;
                if (gameContainer) {
                    await requestFullscreen(gameContainer);
                    setIsFullscreen(true);
                    setShowTip(true);
                    setTimeout(() => setShowTip(false), 3000);
                    
                    // 给iframe获得焦点
                    setTimeout(() => {
                        if (iframeRef.current) {
                            iframeRef.current.focus();
                            iframeRef.current.click();
                        }
                    }, 100);
                }
            } else {
                await exitFullscreen();
                setIsFullscreen(false);
            }
        } catch (err) {
            console.error('Fullscreen operation failed:', err);
            // 移动端fallback处理
            if (isMobile) {
                iframeRef.current.scrollIntoView({ 
                    behavior: 'smooth', 
                    block: 'center' 
                });
                
                // 如果操作失败且是竖屏，建议横屏
                if (screenInfo.isPortrait && screenInfo.width < 768) {
                    setTimeout(() => {
                        setShowOrientationTip(true);
                    }, 500);
                }
            }
        }
    };

    const handleShare = () => {
        setShowShareMenu(!showShareMenu);
    };

    // 获取当前页面信息用于分享
    const shareUrl = typeof window !== 'undefined' ? window.location.href : '';
    const shareTitle = `${title} - Online Game`;
    
    // 分享图标的通用样式 - 根据设备类型动态调整
    const isDark = theme === 'dark';
    const shareIconProps = {
        size: screenInfo.deviceType === 'mobile' ? 32 : 
              screenInfo.deviceType === 'tablet' ? 30 : 24,
        round: true,
        bgStyle: { fill: isDark ? '#2a2a2a' : '#f0f0f0' },
        iconFillColor: isDark ? '#ffffff' : '#000000'
    };

    React.useEffect(() => {
        const handleFullscreenChange = () => {
            const isInFullscreen = !!getFullscreenElement();
            setIsFullscreen(isInFullscreen);
            
            if (isInFullscreen && iframeRef.current) {
                setTimeout(() => {
                    if (iframeRef.current) {
                        iframeRef.current.focus();
                        iframeRef.current.dispatchEvent(new Event('focus'));
                    }
                }, 100);
            }
        };

        // 添加所有浏览器前缀的全屏事件监听
        const events = [
            'fullscreenchange',
            'webkitfullscreenchange',
            'mozfullscreenchange',
            'MSFullscreenChange'
        ];

        events.forEach(event => {
            document.addEventListener(event, handleFullscreenChange);
        });

        return () => {
            events.forEach(event => {
                document.removeEventListener(event, handleFullscreenChange);
            });
        };
    }, []);

    // 点击外部关闭分享菜单 - 支持触摸设备
    React.useEffect(() => {
        const handleClickOutside = (event: MouseEvent | TouchEvent) => {
            if (shareMenuRef.current && !shareMenuRef.current.contains(event.target as Node)) {
                setShowShareMenu(false);
            }
        };

        if (showShareMenu) {
            document.addEventListener('mousedown', handleClickOutside);
            document.addEventListener('touchstart', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            document.removeEventListener('touchstart', handleClickOutside);
        };
    }, [showShareMenu]);

    return (
        <div className="flex flex-col w-full bg-theme-bg-primary dark:bg-dark-secondary backdrop-blur-sm rounded-2xl shadow-xl overflow-hidden border border-theme-border">
            <div className={getGameContainerClass()}>
                {!isLoaded ? (
                    <div className="absolute inset-0 flex flex-col items-center justify-center cursor-pointer group"
                         onClick={handlePlay}
                         onTouchStart={handlePlay} // 添加触摸支持
                    >
                        {/* 背景图片 */}
                        {cover ? (
                            <img
                                src={cover}
                                alt={`${title} cover`}
                                className="absolute inset-0 w-full h-full object-cover blur-sm"
                            />
                        ) : (
                            <img
                                src="/default-cover.jpg"
                                alt="Default cover"
                                className="absolute inset-0 w-full h-full object-cover opacity-50 blur-sm"
                            />
                        )}
                        
                        {/* 渐变遮罩 */}
                        <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-black/40 to-black/60" />
                        
                        {/* 游戏图标区域 - 动态设备适配 */}
                        <div className={`relative z-10 flex flex-col items-center justify-center ${
                            screenInfo.deviceType === 'mobile' ? 'space-y-3' : 
                            screenInfo.deviceType === 'tablet' ? 'space-y-5' : 'space-y-6'
                        }`}>
                            {/* 游戏图标 - 动态大小 */}
                            <div className="relative">
                                <div className={`${
                                    screenInfo.deviceType === 'mobile' ? 'w-20 h-20' : 
                                    screenInfo.deviceType === 'tablet' ? 'w-28 h-28' : 'w-32 h-32'
                                } bg-white rounded-2xl shadow-2xl overflow-hidden transform group-hover:scale-105 group-active:scale-95 transition-transform duration-300`}>
                                    {cover ? (
                                        <img
                                            src={cover}
                                            alt={`${title} icon`}
                                            className="w-full h-full object-cover"
                                        />
                                    ) : (
                                        <div className="w-full h-full bg-gradient-to-br from-primary-500 to-indigo-500 flex items-center justify-center">
                                            <Icon
                                                icon="material-symbols:videogame-asset"
                                                className={`${
                                                    screenInfo.deviceType === 'mobile' ? 'w-10 h-10' : 
                                                    screenInfo.deviceType === 'tablet' ? 'w-14 h-14' : 'w-16 h-16'
                                                } text-white`}
                                            />
                                        </div>
                                    )}
                                </div>
                                {/* 图标周围的光晕效果 */}
                                <div className="absolute -inset-2 bg-gradient-to-r from-yellow-400/20 to-orange-400/20 rounded-3xl blur-xl group-hover:from-yellow-400/30 group-hover:to-orange-400/30 transition-all duration-300" />
                            </div>
                            
                            {/* Play Now 按钮 - 动态设备优化 */}
                            <div className="relative">
                                <button className={`flex items-center space-x-2 ${
                                    screenInfo.deviceType === 'mobile' ? 'px-6 py-3 text-base' : 
                                    screenInfo.deviceType === 'tablet' ? 'px-8 py-4 text-lg' : 'px-6 py-3 text-base'
                                } bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 active:from-yellow-600 active:to-yellow-700 text-black font-bold rounded-full shadow-2xl transform group-hover:scale-105 group-active:scale-95 transition-all duration-300 border-2 border-yellow-300 touch-manipulation`}>
                                    <span>Play Now</span>
                                    <div className={`${
                                        screenInfo.deviceType === 'mobile' ? 'w-5 h-5' : 
                                        screenInfo.deviceType === 'tablet' ? 'w-6 h-6' : 'w-5 h-5'
                                    } bg-black/20 rounded-full flex items-center justify-center`}>
                                        <Icon
                                            icon="material-symbols:play-arrow"
                                            className={`${
                                                screenInfo.deviceType === 'mobile' ? 'w-3 h-3' : 
                                                screenInfo.deviceType === 'tablet' ? 'w-4 h-4' : 'w-3 h-3'
                                            } text-black ml-0.5`}
                                        />
                                    </div>
                                </button>
                                {/* 按钮发光效果 */}
                                <div className="absolute -inset-1 bg-gradient-to-r from-yellow-400/50 to-yellow-500/50 rounded-full blur-lg group-hover:blur-xl transition-all duration-300" />
                            </div>
                        </div>
                    </div>
                ) : (
                    <>
                        <iframe
                            ref={iframeRef}
                            key={key}
                            src={src}
                            title={title}
                            className={`absolute inset-0 w-full h-full border-0 ${isFullscreen ? 'bg-black' : ''}`}
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowFullScreen
                            style={{
                                pointerEvents: 'auto',
                                background: isFullscreen ? '#000' : 'transparent'
                            }}
                        />
                        {showTip && (
                            <div className={`absolute top-4 left-1/2 -translate-x-1/2 bg-black/75 text-white ${isMobile ? 'px-6 py-3 text-base' : 'px-4 py-2 text-sm'} rounded-lg shadow-lg z-10`}>
                                {isMobile && !fullscreenSupported 
                                    ? 'Scroll to the game area for a better experience' 
                                    : 'Press ESC to exit fullscreen'
                                }
                            </div>
                        )}
                        {/* 横屏提示 */}
                        {showOrientationTip && (
                            <div className="absolute inset-0 bg-black/80 flex items-center justify-center z-20">
                                <div className="bg-white dark:bg-gray-800 rounded-xl p-6 mx-4 max-w-sm text-center shadow-2xl">
                                    <div className="mb-4">
                                        <Icon icon="material-symbols:screen-rotation" className="w-12 h-12 mx-auto text-primary-500 animate-pulse" />
                                    </div>
                                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                                        Rotate for Better Experience
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                                        Turn your device to landscape mode for a better gaming experience
                                    </p>
                                    <div className="flex gap-2">
                                        <button
                                            onClick={() => setShowOrientationTip(false)}
                                            className="flex-1 px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-white rounded-lg text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
                                        >
                                            Continue Portrait
                                        </button>
                                        <button
                                            onClick={() => {
                                                setShowOrientationTip(false);
                                                // 尝试触发设备旋转（仅在支持的浏览器中）
                                                if ('screen' in window && 'orientation' in (window.screen as any)) {
                                                    try {
                                                        (window.screen as any).orientation.lock('landscape').catch(() => {});
                                                    } catch (e) {}
                                                }
                                            }}
                                            className="flex-1 px-4 py-2 bg-primary-500 text-white rounded-lg text-sm font-medium hover:bg-primary-600 transition-colors"
                                        >
                                            Got it
                                        </button>
                                    </div>
                                </div>
                            </div>
                        )}
                    </>
                )}
            </div>
            <div className={`flex items-center justify-between ${
                screenInfo.deviceType === 'mobile' ? 'px-3 py-3' : 
                screenInfo.deviceType === 'tablet' ? 'px-4 py-3' : 'px-4 py-3'
            } border-t border-theme-border`}>
                <div className="flex items-center gap-2 flex-1 min-w-0">
                    {cover && (
                        <img 
                            src={cover} 
                            alt={`${title} cover`} 
                            className={`${
                                screenInfo.deviceType === 'mobile' ? 'w-10 h-10' : 
                                screenInfo.deviceType === 'tablet' ? 'w-12 h-12' : 'w-8 h-8'
                            } rounded-lg object-cover flex-shrink-0`} 
                        />
                    )}
                    <p className={`${
                        screenInfo.deviceType === 'mobile' ? 'text-base' : 
                        screenInfo.deviceType === 'tablet' ? 'text-lg' : 'text-base'
                    } font-medium text-theme-text-primary truncate`}>{title}</p>
                </div>
                <div className={`flex items-center ${screenInfo.deviceType === 'mobile' ? 'gap-1' : 'gap-1'} relative`}>
                    {isLoaded && (
                        <button
                            onClick={handleReload}
                            className={`${
                                screenInfo.deviceType === 'mobile' ? 'p-3' : 
                                screenInfo.deviceType === 'tablet' ? 'p-2.5' : 'p-2'
                            } text-theme-text-secondary hover:text-primary-500 hover:bg-theme-bg-secondary active:bg-theme-bg-secondary rounded-lg transition-colors touch-manipulation`}
                            title="Reload"
                        >
                            <Icon 
                                icon="material-symbols:refresh" 
                                className={`${
                                    screenInfo.deviceType === 'mobile' ? 'w-6 h-6' : 
                                    screenInfo.deviceType === 'tablet' ? 'w-6 h-6' : 'w-5 h-5'
                                }`} 
                            />
                        </button>
                    )}
                    <button
                        onClick={handleFullscreen}
                        className={`${
                            screenInfo.deviceType === 'mobile' ? 'p-3' : 
                            screenInfo.deviceType === 'tablet' ? 'p-2.5' : 'p-2'
                        } text-theme-text-secondary hover:text-primary-500 hover:bg-theme-bg-secondary active:bg-theme-bg-secondary rounded-lg transition-colors touch-manipulation ${
                            !fullscreenSupported && isMobile ? 'opacity-50' : ''
                        }`}
                        title={
                            isMobile && !fullscreenSupported ? "Scroll to optimize" : 
                            screenInfo.isPortrait && screenInfo.deviceType === 'mobile' ? "Fullscreen / Rotate" : "Fullscreen"
                        }
                    >
                        <Icon 
                            icon={isFullscreen ? "material-symbols:fullscreen-exit" : "material-symbols:fullscreen"} 
                            className={`${
                                screenInfo.deviceType === 'mobile' ? 'w-6 h-6' : 
                                screenInfo.deviceType === 'tablet' ? 'w-6 h-6' : 'w-5 h-5'
                            }`} 
                        />
                    </button>
                    <div className="relative" ref={shareMenuRef}>
                        <button
                            onClick={handleShare}
                            className={`${
                                screenInfo.deviceType === 'mobile' ? 'p-3' : 
                                screenInfo.deviceType === 'tablet' ? 'p-2.5' : 'p-2'
                            } text-theme-text-secondary hover:text-primary-500 hover:bg-theme-bg-secondary active:bg-theme-bg-secondary rounded-lg transition-colors touch-manipulation`}
                            title="Share"
                        >
                            <Icon 
                                icon="material-symbols:share" 
                                className={`${
                                    screenInfo.deviceType === 'mobile' ? 'w-6 h-6' : 
                                    screenInfo.deviceType === 'tablet' ? 'w-6 h-6' : 'w-5 h-5'
                                }`} 
                            />
                        </button>
                        {showShareMenu && (
                            <div className={`absolute right-0 bottom-full mb-2 bg-theme-bg-primary dark:bg-dark-primary border border-theme-border rounded-lg shadow-lg ${
                                screenInfo.deviceType === 'mobile' ? 'p-4' : 
                                screenInfo.deviceType === 'tablet' ? 'p-4' : 'p-3'
                            } z-10 ${
                                screenInfo.deviceType === 'mobile' ? 'min-w-[320px]' : 
                                screenInfo.deviceType === 'tablet' ? 'min-w-[300px]' : 'min-w-[280px]'
                            }`}>
                                <div className="flex items-center gap-2 mb-2">
                                    <Icon 
                                        icon="material-symbols:share" 
                                        className={`${
                                            screenInfo.deviceType === 'mobile' ? 'w-5 h-5' : 
                                            screenInfo.deviceType === 'tablet' ? 'w-5 h-5' : 'w-4 h-4'
                                        } text-theme-text-secondary`} 
                                    />
                                    <span className={`${
                                        screenInfo.deviceType === 'mobile' ? 'text-base' : 
                                        screenInfo.deviceType === 'tablet' ? 'text-base' : 'text-sm'
                                    } font-medium text-theme-text-primary`}>Share to</span>
                                </div>
                                <div className={`flex flex-wrap ${
                                    screenInfo.deviceType === 'mobile' ? 'gap-3' : 
                                    screenInfo.deviceType === 'tablet' ? 'gap-3' : 'gap-2'
                                }`}>
                                    <FacebookShareButton 
                                        url={shareUrl} 
                                        title={shareTitle}
                                        className="hover:opacity-80 active:opacity-60 transition-opacity"
                                    >
                                        <FacebookIcon {...shareIconProps} />
                                    </FacebookShareButton>
                                    <TwitterShareButton 
                                        url={shareUrl} 
                                        title={shareTitle} 
                                        className="hover:opacity-80 active:opacity-60 transition-opacity"
                                    >
                                        <TwitterIcon {...shareIconProps} />
                                    </TwitterShareButton>
                                    <TelegramShareButton 
                                        url={shareUrl} 
                                        title={shareTitle} 
                                        className="hover:opacity-80 active:opacity-60 transition-opacity"
                                    >
                                        <TelegramIcon {...shareIconProps} />
                                    </TelegramShareButton>
                                    <WhatsappShareButton 
                                        url={shareUrl} 
                                        title={shareTitle} 
                                        className="hover:opacity-80 active:opacity-60 transition-opacity"
                                    >
                                        <WhatsappIcon {...shareIconProps} />
                                    </WhatsappShareButton>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}