import type { ThemeConfig } from '../types'
import Link from 'next/link'

export function Footer({ themeConfig }: { themeConfig?: ThemeConfig }) {
    const siteName = themeConfig?.siteName || 'Site Name'
    const footerConfig = themeConfig?.footer

    // Default configuration
    const aboutConfig = footerConfig?.about || {
        title: 'About',
        description: 'Welcome to our website! We are dedicated to providing high-quality content and services to deliver the best experience for our users.'
    }

    const friendsConfig = footerConfig?.friends || {
        title: 'Friends',
        links: [
            { name: 'Example Site 1', url: 'https://example1.com', description: 'Quality content sharing' },
            { name: 'Example Site 2', url: 'https://example2.com', description: 'Tech discussions' },
            { name: 'Example Site 3', url: 'https://example3.com', description: 'Resource sharing' },
        ]
    }

    const legalConfig = footerConfig?.legal || {
        title: 'Legal',
        links: [
            { name: 'Privacy Policy', url: '/privacy-policy' },
            { name: 'Terms of Service', url: '/terms-of-service' },
        ]
    }

    return (
        <footer className="bg-theme-bg-primary/80 dark:bg-dark-secondary/80 backdrop-blur-sm border-t border-theme-border">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                {/* 主要内容区域：左右布局 */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                    {/* 左侧：About 网站信息 */}
                    <div className="space-y-4 max-w-md">
                        <p className="text-lg font-semibold text-theme-text-primary">
                            {aboutConfig.title}
                        </p>
                        <div className="space-y-3">
                            <Link 
                                href="/" 
                                className="block text-xl font-bold text-primary-500 hover:text-primary-600 transition-colors"
                            >
                                {siteName}
                            </Link>
                            <p className="text-sm text-theme-text-secondary leading-relaxed">
                                {aboutConfig.description}
                            </p>
                        </div>
                    </div>

                    {/* 右侧：Friends 和 Legal */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        {/* Friends 友情链接 */}
                        <div className="space-y-4">
                            <p className="text-lg font-semibold text-theme-text-primary">
                                {friendsConfig.title}
                            </p>
                            <ul className="space-y-2">
                                {friendsConfig.links?.map((link, index) => (
                                    <li key={index}>
                                        <a
                                            href={link.url}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="group block py-1 rounded-md hover:bg-theme-bg-secondary/50 transition-colors"
                                        >
                                            <div className="text-sm font-medium text-theme-text-primary group-hover:text-primary-500 transition-colors">
                                                {link.name}
                                            </div>
                                            {link.description && (
                                                <div className="text-xs text-theme-text-secondary mt-1">
                                                    {link.description}
                                                </div>
                                            )}
                                        </a>
                                    </li>
                                ))}
                            </ul>
                        </div>

                        {/* Legal 法律条款 */}
                        <div className="space-y-4">
                            <p className="text-lg font-semibold text-theme-text-primary">
                                {legalConfig.title}
                            </p>
                            <ul className="space-y-2">
                                {legalConfig.links?.map((link, index) => (
                                    <li key={index}>
                                        <Link
                                            href={link.url}
                                            className="text-sm text-theme-text-secondary hover:text-primary-500 transition-colors block py-1"
                                        >
                                            {link.name}
                                        </Link>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    </div>
                </div>

                {/* 分隔线 */}
                <div className="border-t border-theme-border mb-6"></div>

                {/* 底部版权区域 */}
                <div className="text-center text-sm text-theme-text-secondary">
                    {new Date().getFullYear()} © {' '}
                    <Link 
                        href="/" 
                        className="hover:text-primary-500 transition-colors"
                    >
                        {siteName}
                    </Link>
                    . All rights reserved.
                </div>
            </div>
        </footer>
    )
} 